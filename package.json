{"name": "ai-chatbot", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbo", "build": "tsx lib/db/migrate && next build", "start": "next start", "lint": "next lint && biome lint --write --unsafe", "lint:fix": "next lint --fix && biome lint --write --unsafe", "format": "biome format --write", "db:generate": "drizzle-kit generate", "db:migrate": "npx tsx lib/db/migrate.ts", "db:studio": "drizzle-kit studio", "db:push": "drizzle-kit push", "db:pull": "drizzle-kit pull", "db:check": "drizzle-kit check", "db:up": "drizzle-kit up"}, "dependencies": {"@ai-sdk/openai": "1.0.19", "@ai-sdk/openai-compatible": "^0.1.9", "@codemirror/lang-javascript": "^6.2.2", "@codemirror/lang-python": "^6.1.6", "@codemirror/state": "^6.5.0", "@codemirror/theme-one-dark": "^6.1.2", "@codemirror/view": "^6.35.3", "@heroicons/react": "^2.2.0", "@radix-ui/react-alert-dialog": "^1.1.2", "@radix-ui/react-avatar": "^1.1.9", "@radix-ui/react-dialog": "^1.1.2", "@radix-ui/react-dropdown-menu": "^2.1.2", "@radix-ui/react-icons": "^1.3.0", "@radix-ui/react-label": "^2.1.0", "@radix-ui/react-progress": "^1.1.6", "@radix-ui/react-scroll-area": "^1.2.8", "@radix-ui/react-select": "^2.1.2", "@radix-ui/react-separator": "^1.1.0", "@radix-ui/react-slot": "^1.1.0", "@radix-ui/react-tabs": "^1.1.11", "@radix-ui/react-tooltip": "^1.1.3", "@radix-ui/react-visually-hidden": "^1.1.0", "@vercel/analytics": "^1.3.1", "@vercel/blob": "^0.24.1", "@vercel/postgres": "^0.10.0", "ai": "4.0.36", "bcrypt-ts": "^5.0.2", "class-variance-authority": "^0.7.0", "classnames": "^2.5.1", "clsx": "^2.1.1", "codemirror": "^6.0.1", "date-fns": "^4.1.0", "diff-match-patch": "^1.0.5", "dotenv": "^16.4.5", "drizzle-orm": "^0.34.0", "fast-deep-equal": "^3.1.3", "framer-motion": "^11.3.19", "geist": "^1.3.1", "jwt-decode": "^4.0.0", "lucide-react": "^0.446.0", "nanoid": "^5.0.8", "next": "15.0.3-canary.2", "next-auth": "5.0.0-beta.25", "next-themes": "^0.3.0", "orderedmap": "^2.1.1", "pnpm": "^10.2.0", "postgres": "^3.4.4", "prosemirror-example-setup": "^1.2.3", "prosemirror-inputrules": "^1.4.0", "prosemirror-markdown": "^1.13.1", "prosemirror-model": "^1.23.0", "prosemirror-schema-basic": "^1.2.3", "prosemirror-schema-list": "^1.4.1", "prosemirror-state": "^1.4.3", "prosemirror-view": "^1.34.3", "react": "19.0.0-rc-45804af1-20241021", "react-dom": "19.0.0-rc-45804af1-20241021", "react-markdown": "^9.0.1", "react-resizable-panels": "^2.1.7", "remark-gfm": "^4.0.0", "server-only": "^0.0.1", "sonner": "^1.5.0", "swr": "^2.2.5", "tailwind-merge": "^2.5.2", "tailwindcss-animate": "^1.0.7", "usehooks-ts": "^3.1.0", "zod": "^3.23.8"}, "devDependencies": {"@biomejs/biome": "1.9.4", "@tailwindcss/typography": "^0.5.15", "@types/d3-scale": "^4.0.8", "@types/node": "^22.8.6", "@types/pdf-parse": "^1.1.4", "@types/react": "^18", "@types/react-dom": "^18", "drizzle-kit": "^0.25.0", "eslint": "^8.57.0", "eslint-config-next": "14.2.5", "eslint-config-prettier": "^9.1.0", "eslint-import-resolver-typescript": "^3.6.3", "eslint-plugin-tailwindcss": "^3.17.5", "postcss": "^8", "tailwindcss": "^3.4.1", "tsx": "^4.19.1", "typescript": "^5.6.3"}}