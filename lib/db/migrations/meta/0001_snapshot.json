{"id": "f3d3437c-4735-4c91-80af-1014048a904e", "prevId": "715ec9ec-6715-4d0f-9f6c-9b5c7f09827c", "version": "7", "dialect": "postgresql", "tables": {"public.Suggestion": {"name": "Suggestion", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": false, "notNull": true, "default": "gen_random_uuid()"}, "documentId": {"name": "documentId", "type": "uuid", "primaryKey": false, "notNull": true}, "documentCreatedAt": {"name": "documentCreatedAt", "type": "timestamp", "primaryKey": false, "notNull": true}, "originalText": {"name": "originalText", "type": "text", "primaryKey": false, "notNull": true}, "suggestedText": {"name": "suggestedText", "type": "text", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "isResolved": {"name": "isResolved", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "userId": {"name": "userId", "type": "uuid", "primaryKey": false, "notNull": true}, "createdAt": {"name": "createdAt", "type": "timestamp", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {"Suggestion_userId_User_id_fk": {"name": "Suggestion_userId_User_id_fk", "tableFrom": "Suggestion", "tableTo": "User", "columnsFrom": ["userId"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "Suggestion_documentId_documentCreatedAt_Document_id_createdAt_fk": {"name": "Suggestion_documentId_documentCreatedAt_Document_id_createdAt_fk", "tableFrom": "Suggestion", "tableTo": "Document", "columnsFrom": ["documentId", "documentCreatedAt"], "columnsTo": ["id", "createdAt"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {"Suggestion_id_pk": {"name": "Suggestion_id_pk", "columns": ["id"]}}, "uniqueConstraints": {}}, "public.Chat": {"name": "Cha<PERSON>", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "createdAt": {"name": "createdAt", "type": "timestamp", "primaryKey": false, "notNull": true}, "messages": {"name": "messages", "type": "json", "primaryKey": false, "notNull": true}, "userId": {"name": "userId", "type": "uuid", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {"Chat_userId_User_id_fk": {"name": "Chat_userId_User_id_fk", "tableFrom": "Cha<PERSON>", "tableTo": "User", "columnsFrom": ["userId"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "public.Document": {"name": "Document", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": false, "notNull": true, "default": "gen_random_uuid()"}, "createdAt": {"name": "createdAt", "type": "timestamp", "primaryKey": false, "notNull": true}, "title": {"name": "title", "type": "text", "primaryKey": false, "notNull": true}, "content": {"name": "content", "type": "text", "primaryKey": false, "notNull": false}, "userId": {"name": "userId", "type": "uuid", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {"Document_userId_User_id_fk": {"name": "Document_userId_User_id_fk", "tableFrom": "Document", "tableTo": "User", "columnsFrom": ["userId"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {"Document_id_createdAt_pk": {"name": "Document_id_createdAt_pk", "columns": ["id", "createdAt"]}}, "uniqueConstraints": {}}, "public.User": {"name": "User", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "email": {"name": "email", "type": "<PERSON><PERSON><PERSON>(64)", "primaryKey": false, "notNull": true}, "password": {"name": "password", "type": "<PERSON><PERSON><PERSON>(64)", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}}, "enums": {}, "schemas": {}, "sequences": {}, "_meta": {"columns": {}, "schemas": {}, "tables": {}}}